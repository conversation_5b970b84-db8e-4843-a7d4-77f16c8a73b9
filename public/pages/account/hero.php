<?php
// Sample user data - in a real app, this would come from a database
$user = [
    'name' => 'Mustapha',
    'email' => '<EMAIL>',
    'avatar' => IMAGE_SRC . 'profile.png',
    'joinDate' => '2024-01-15'
];

// Sample subscription data
$subscriptions = [
    [
        'id' => 1,
        'plan' => 'Basic Plan',
        'price' => '$9.99',
        'period' => 'month',
        'status' => 'active',
        'expiresOn' => '2025-12-01',
        'autoRenew' => true
    ],
    [
        'id' => 2,
        'plan' => 'Standard Plan',
        'price' => '$14.99',
        'period' => 'month',
        'status' => 'active',
        'expiresOn' => '2025-12-01',
        'autoRenew' => true
    ],
    [
        'id' => 3,
        'plan' => 'Premium Plan',
        'price' => '$19.99',
        'period' => 'month',
        'status' => 'expired',
        'expiresOn' => '2024-11-01',
        'autoRenew' => false
    ]
];

?>

<div
    class="w-full p-6 lg:p-12 bg-stone-950 rounded-xl outline outline-1 outline-offset-[-1px] outline-neutral-800 flex flex-col justify-start items-center gap-8 lg:gap-12 hover:outline-neutral-700 transition-colors duration-300">
    <div
        class="self-stretch h-64 lg:h-96 p-6 lg:p-12 relative bg-neutral-900 rounded-xl outline outline-2 outline-neutral-800 flex flex-col justify-start items-center gap-8">
        <!-- Dynamic Movie Background Grid -->
        <div
            class="w-full h-full p-2.5 left-0 top-0 absolute rounded-xl inline-flex justify-center items-start gap-2.5 overflow-hidden">
            <div class="w-full h-full flex justify-start items-start gap-2 lg:gap-5 bg-neutral-900 rounded-xl">
                <img class="flex-1 self-stretch rounded-lg lg:rounded-xl object-cover"
                    src="<?= IMAGE_SRC . "hero-bg.webp" ?>" alt="Movie poster" loading="lazy" />
            </div>
            <div class="w-full h-full left-0 top-0 absolute bg-zinc-950/70 rounded-xl"></div>
        </div>
        <!-- User Avatar -->
        <img class="w-24 h-24 lg:w-40 lg:h-40 absolute left-1/2 -bottom-4 transform -translate-x-1/2 rounded-full border-4 border-white shadow-xl z-10"
            src="<?= htmlspecialchars($user['avatar']) ?>" alt="<?= htmlspecialchars($user['name']) ?> avatar" />
    </div>
    <!-- User Info -->
    <div class="flex flex-col justify-center items-center gap-4">
        <div
            class="text-center tb-sm:text-transparent text-white tb-sm:bg-clip-text tb-sm:bg-gradient-to-r from-white to-[#CACACA] text-xl lg:text-2xl font-bold font-['Manrope'] leading-normal [text-shadow:_0px_25px_50px_rgb(0_0_0_/_0.25)]">
            Hello, <?= htmlspecialchars($user['name']) ?>!</div>
        <div
            class="text-center tb-sm:text-transparent text-white tb-sm:bg-clip-text tb-sm:bg-gradient-to-r from-white to-[#CACACA] text-base lg:text-lg font-normal font-['Manrope'] leading-none [text-shadow:_0px_25px_50px_rgb(0_0_0_/_0.25)]">
            <?= htmlspecialchars($user['email']) ?>
        </div>
    </div>

    <!-- Navigation Buttons -->
    <div class="flex flex-wrap justify-center items-start gap-3 lg:gap-5">
        <a href="/account/settings"
            class="px-4 lg:px-6 py-3 lg:py-3.5 bg-stone-900 hover:bg-stone-800 rounded-[10px] outline outline-2 outline-neutral-800 hover:outline-neutral-700 flex justify-center items-center gap-2.5 transition-all duration-300">
            <div class="text-white text-sm lg:text-lg font-medium font-['Manrope'] leading-relaxed">Account
                Settings</div>
        </a>
        <div
            class="px-4 lg:px-6 py-3 lg:py-3.5 bg-red-600 rounded-[10px] flex justify-center items-center gap-2.5 shadow-lg">
            <div class="text-white text-sm lg:text-lg font-medium font-['Manrope'] leading-relaxed">Your
                Subscriptions</div>
        </div>
    </div>
</div>